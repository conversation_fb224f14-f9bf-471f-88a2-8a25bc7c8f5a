<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Sistema de Cotización</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">Haz clic en el botón para procesar una cotización de ejemplo.</p>
                    
                    <button type="button" 
                            class="btn btn-success btn-lg"
                            wire:click="procesarCotizacion">
                        <i class="fas fa-calculator me-2"></i>
                        Procesar Cotización
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Componente de tabla de cotización -->
    @livewire('cotizacion-table')
</div>

@push('scripts')
<script>
    // Escuchar el evento de continuar cotización
    document.addEventListener('livewire:init', () => {
        Livewire.on('continuar-cotizacion', () => {
            // Aquí puedes agregar tu lógica personalizada
            console.log('Continuando con la cotización...');
            
            // Ejemplo: redirigir a otra página
            // window.location.href = '/descargar-cotizacion';
            
            // Ejemplo: mostrar mensaje de éxito
            alert('¡Cotización procesada exitosamente!');
            
            // Ejemplo: llamar a una función personalizada
            // cerrar(); // tu función original
        });
    });
</script>
@endpush
