<div>
    @if($showModal)
        <!-- Modal Bootstrap Style -->
        <div class="modal fade show" style="display: block;" tabindex="-1" aria-labelledby="tabla_resultados" aria-modal="true" role="dialog">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="tabla_resultados">{{ $modalTitle }}</h5>
                        <button type="button" class="btn-close" wire:click="closeModal" aria-label="Close"></button>
                    </div>

                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">Aseguradoras</th>
                                        <th scope="col">Prima</th>
                                        <th scope="col">Comentario</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($planes as $plan)
                                        <tr>
                                            <td class="fw-medium">{{ $plan['aseguradora'] ?? '' }}</td>
                                            <td>
                                                <span class="text-success fw-bold">
                                                    RD$ {{ $this->getFormattedPrima($plan) }}
                                                </span>
                                            </td>
                                            <td>{{ $plan['comentario'] ?? '' }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center text-muted">
                                                No hay planes disponibles
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeModal">Cerrar</button>
                        @if($this->getPlanesConTotal()->count() > 0)
                            <button type="button" class="btn btn-primary" wire:click="continuar">Continuar</button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modal Backdrop -->
        <div class="modal-backdrop fade show" wire:click="closeModal"></div>
    @endif

    <style>
        .modal.show {
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.075);
        }
        
        .btn-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            font-weight: 700;
            line-height: 1;
            color: #000;
            text-shadow: 0 1px 0 #fff;
            opacity: 0.5;
            cursor: pointer;
        }
        
        .btn-close:hover {
            opacity: 0.75;
        }
        
        .btn-close::before {
            content: "×";
        }
    </style>
</div>
