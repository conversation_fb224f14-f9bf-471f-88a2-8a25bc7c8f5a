<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ejemplo Cotización Table</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="bg-white rounded-lg shadow p-6">
            <h1 class="text-2xl font-bold mb-4">Ejemplo de Tabla de Cotización</h1>
            
            <button type="button" 
                    class="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    onclick="mostrarCotizacion()">
                Mostrar Cotización
            </button>

            <!-- Componente Livewire -->
            @livewire('cotizacion-table')
        </div>
    </div>

    @livewireScripts

    <script>
        function mostrarCotizacion() {
            // Datos de ejemplo
            const planesEjemplo = [
                {
                    aseguradora: "Seguros Universal",
                    total: 15000.50,
                    monto_mantenimiento: 500.00,
                    comentario: "Cobertura completa con deducible bajo"
                },
                {
                    aseguradora: "Mapfre BHD",
                    total: 12500.75,
                    monto_mantenimiento: 0,
                    comentario: "Excelente relación precio-calidad"
                },
                {
                    aseguradora: "Seguros Pepín",
                    total: 18200.00,
                    monto_mantenimiento: 750.00,
                    comentario: "Cobertura premium con servicios adicionales"
                },
                {
                    aseguradora: "La Colonial",
                    total: 0,
                    monto_mantenimiento: 0,
                    comentario: "No disponible para este perfil"
                }
            ];

            // Disparar evento para mostrar la tabla
            Livewire.dispatch('showCotizacionTable', {
                planes: planesEjemplo,
                cuentaId: "3222373000005967119" // ID de ejemplo
            });
        }

        // Escuchar evento de continuar
        document.addEventListener('livewire:init', () => {
            Livewire.on('continuar-cotizacion', () => {
                alert('¡Continuando con la cotización!');
                // Aquí puedes agregar tu lógica personalizada
                console.log('Evento continuar-cotizacion disparado');
            });
        });
    </script>
</body>
</html>
