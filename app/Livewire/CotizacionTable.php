<?php

namespace App\Livewire;

use Livewire\Component;

class CotizacionTable extends Component
{
    public $planes = [];
    public $showModal = false;
    public $modalTitle = '¡Cotización exitosa! Presiona "Continuar" para descargar la cotización y emitir.';
    public $cuentaId = null;

    protected $listeners = ['showCotizacionTable'];

    public function mount($planes = [], $cuentaId = null)
    {
        $this->planes = $planes;
        $this->cuentaId = $cuentaId;
    }

    public function showCotizacionTable($planes = [], $cuentaId = null)
    {
        $this->planes = $planes;
        $this->cuentaId = $cuentaId;
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function continuar()
    {
        $this->dispatch('continuar-cotizacion');
        $this->closeModal();
    }

    public function getPlanesConTotal()
    {
        return collect($this->planes)->filter(function ($plan) {
            return ($plan['total'] ?? 0) > 0;
        });
    }

    public function getFormattedPrima($plan)
    {
        $total = $plan['total'] ?? 0;
        $mantenimiento = $plan['monto_mantenimiento'] ?? 0;
        
        if ($this->cuentaId === "3222373000005967119" && $mantenimiento > 0) {
            return number_format($total + $mantenimiento, 2);
        }
        
        return number_format($total, 2);
    }

    public function render()
    {
        return view('livewire.cotizacion-table');
    }
}
