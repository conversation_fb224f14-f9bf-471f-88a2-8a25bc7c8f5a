<?php

namespace App\Livewire;

use Livewire\Component;

class CotizacionExample extends Component
{
    public function procesarCotizacion()
    {
        // Simular procesamiento de cotización
        $planes = [
            [
                'aseguradora' => 'Seguros Universal',
                'total' => 15000.50,
                'monto_mantenimiento' => 500.00,
                'comentario' => 'Cobertura completa con deducible bajo'
            ],
            [
                'aseguradora' => 'Mapfre BHD',
                'total' => 12500.75,
                'monto_mantenimiento' => 0,
                'comentario' => 'Excelente relación precio-calidad'
            ],
            [
                'aseguradora' => 'Seguros P<PERSON>ín',
                'total' => 18200.00,
                'monto_mantenimiento' => 750.00,
                'comentario' => 'Cobertura premium con servicios adicionales'
            ],
            [
                'aseguradora' => 'La Colonial',
                'total' => 0,
                'monto_mantenimiento' => 0,
                'comentario' => 'No disponible para este perfil'
            ]
        ];

        // Obtener cuenta ID de la sesión (simulado)
        $cuentaId = session('cuenta_id', '3222373000005967119');

        // Disparar evento para mostrar la tabla
        $this->dispatch('showCotizacionTable', 
            planes: $planes, 
            cuentaId: $cuentaId
        );
    }

    public function render()
    {
        return view('livewire.cotizacion-example');
    }
}
