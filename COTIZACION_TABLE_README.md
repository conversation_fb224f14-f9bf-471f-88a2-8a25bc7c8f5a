# Componente CotizacionTable - Livewire

Este componente Livewire recrea la funcionalidad de la tabla de cotización original con estilos similares a Filament.

## Archivos Creados

1. **`app/Livewire/CotizacionTable.php`** - Componente principal
2. **`resources/views/livewire/cotizacion-table.blade.php`** - Vista con estilos Tailwind (estilo Filament)
3. **`resources/views/livewire/cotizacion-table-bootstrap.blade.php`** - Vista alternativa con Bootstrap
4. **`app/Livewire/CotizacionExample.php`** - Ejemplo de uso
5. **`resources/views/livewire/cotizacion-example.blade.php`** - Vista del ejemplo

## Uso Básico

### 1. Incluir el componente en tu vista

```blade
@livewire('cotizacion-table')
```

### 2. Mostrar la tabla desde otro componente

```php
// En tu componente Livewire
public function mostrarCotizacion()
{
    $planes = [
        [
            'aseguradora' => 'Seguros Universal',
            'total' => 15000.50,
            'monto_mantenimiento' => 500.00,
            'comentario' => 'Cobertura completa'
        ],
        // ... más planes
    ];

    $this->dispatch('showCotizacionTable', 
        planes: $planes, 
        cuentaId: session('cuenta_id')
    );
}
```

### 3. Desde JavaScript

```javascript
Livewire.dispatch('showCotizacionTable', {
    planes: planesArray,
    cuentaId: 'tu_cuenta_id'
});
```

## Estructura de Datos

El array `$planes` debe tener la siguiente estructura:

```php
[
    [
        'aseguradora' => 'Nombre de la aseguradora',
        'total' => 15000.50, // Monto numérico
        'monto_mantenimiento' => 500.00, // Opcional
        'comentario' => 'Descripción del plan'
    ],
    // ... más planes
]
```

## Eventos

### Eventos que escucha:
- `showCotizacionTable` - Muestra la tabla con los datos proporcionados

### Eventos que emite:
- `continuar-cotizacion` - Se dispara cuando el usuario hace clic en "Continuar"

## Personalización

### Cambiar el título del modal

```php
// En tu componente
$this->dispatch('showCotizacionTable', [
    'planes' => $planes,
    'cuentaId' => $cuentaId,
    'modalTitle' => 'Tu título personalizado'
]);
```

### Escuchar el evento de continuar

```javascript
document.addEventListener('livewire:init', () => {
    Livewire.on('continuar-cotizacion', () => {
        // Tu lógica personalizada aquí
        console.log('Continuando...');
    });
});
```

## Estilos

### Versión Tailwind (Filament-like)
Usa `cotizacion-table.blade.php` - Requiere Tailwind CSS

### Versión Bootstrap
Usa `cotizacion-table-bootstrap.blade.php` - Compatible con Bootstrap 5

Para cambiar la vista, modifica el método `render()` en `CotizacionTable.php`:

```php
public function render()
{
    return view('livewire.cotizacion-table-bootstrap'); // Para Bootstrap
}
```

## Funcionalidades

- ✅ Modal responsive
- ✅ Tabla con hover effects
- ✅ Formateo automático de montos
- ✅ Lógica condicional para monto de mantenimiento
- ✅ Botón "Continuar" solo aparece si hay planes con total > 0
- ✅ Eventos Livewire para integración
- ✅ Estilos similares a Filament
- ✅ Alternativa con Bootstrap

## Ejemplo Completo

Visita `/ejemplo-cotizacion` para ver el componente en acción (requiere configurar la ruta).

## Integración con tu código existente

Para reemplazar tu código PHP original, simplemente:

1. Incluye el componente donde necesites la tabla
2. Dispara el evento `showCotizacionTable` con tus datos
3. Escucha el evento `continuar-cotizacion` para manejar la acción de continuar
